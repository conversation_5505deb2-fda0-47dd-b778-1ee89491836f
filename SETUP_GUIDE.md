# UE5 Blueprint Plugin Generator - Setup Guide

## Installation and Compilation Guide for UE5.4

This guide will walk you through installing and compiling the UE5 Blueprint Plugin Generator in your Unreal Engine 5.4 installation.

## Prerequisites

### Required Software
- ✅ **Unreal Engine 5.4** (Installed at: `C:\Program Files\Epic Games\UE_5.4`)
- **Visual Studio 2019/2022** with C++ development tools
- **Windows SDK** (latest version)
- **Git** (for version control, optional)

### Verify UE5.4 Installation
1. Navigate to: `C:\Program Files\Epic Games\UE_5.4`
2. Verify these folders exist:
   - `Engine/`
   - `Engine/Plugins/`
   - `Engine/Binaries/`
   - `Engine/Source/`

## Installation Methods

### Method 1: Engine Plugin (Recommended)
Install as a global engine plugin available to all projects.

#### Step 1: Copy Plugin to Engine
1. **Navigate to UE5.4 plugins directory**:
   ```
   C:\Program Files\Epic Games\UE_5.4\Engine\Plugins\Developer\
   ```

2. **Create plugin directory**:
   ```
   C:\Program Files\Epic Games\UE_5.4\Engine\Plugins\Developer\UE5BlueprintGenerator\
   ```

3. **Copy all plugin files** from your current directory to the new location:
   ```
   Source: C:\Users\<USER>\Desktop\blueprint\
   Target: C:\Program Files\Epic Games\UE_5.4\Engine\Plugins\Developer\UE5BlueprintGenerator\
   ```

#### Step 2: Regenerate Engine Project Files
1. **Open Command Prompt as Administrator**
2. **Navigate to UE5.4 directory**:
   ```cmd
   cd "C:\Program Files\Epic Games\UE_5.4"
   ```
3. **Run project file generation**:
   ```cmd
   GenerateProjectFiles.bat
   ```

#### Step 3: Build Engine with Plugin
1. **Open** `UE5.sln` in Visual Studio (located in UE5.4 directory)
2. **Set build configuration** to `Development Editor`
3. **Build solution** (Ctrl+Shift+B)

### Method 2: Project Plugin
Install as a project-specific plugin.

#### Step 1: Create or Open UE5 Project
1. **Launch UE5.4**
2. **Create new project** or **open existing project**

#### Step 2: Copy Plugin to Project
1. **Navigate to your project directory**:
   ```
   YourProject\Plugins\
   ```
2. **Create plugin directory**:
   ```
   YourProject\Plugins\UE5BlueprintGenerator\
   ```
3. **Copy all plugin files** to the new location

#### Step 3: Regenerate Project Files
1. **Close UE5 Editor**
2. **Right-click** on `YourProject.uproject`
3. **Select** "Generate Visual Studio project files"
4. **Open** `YourProject.sln` in Visual Studio
5. **Build solution**

## Plugin File Structure

Ensure your plugin directory contains:

```
UE5BlueprintGenerator/
├── UE5BlueprintGenerator.uplugin
├── Source/
│   └── UE5BlueprintGenerator/
│       ├── UE5BlueprintGenerator.Build.cs
│       ├── Public/
│       │   ├── UE5BlueprintGeneratorModule.h
│       │   ├── AIModelConnector.h
│       │   ├── AI/
│       │   ├── BlueprintGeneration/
│       │   ├── CodeGeneration/
│       │   ├── NLP/
│       │   ├── Settings/
│       │   └── UI/
│       └── Private/
│           ├── UE5BlueprintGeneratorModule.cpp
│           ├── AIModelConnector.cpp
│           ├── AI/
│           ├── BlueprintGeneration/
│           ├── CodeGeneration/
│           ├── NLP/
│           ├── Settings/
│           └── UI/
└── docs/ (optional)
```

## Compilation Steps

### Using Command Line (Recommended)

1. **Open Command Prompt as Administrator**

2. **Navigate to UE5.4 directory**:
   ```cmd
   cd "C:\Program Files\Epic Games\UE_5.4"
   ```

3. **Build the plugin**:
   ```cmd
   Engine\Build\BatchFiles\Build.bat UE5BlueprintGenerator Win64 Development -Project="C:\Program Files\Epic Games\UE_5.4\Engine\Plugins\Developer\UE5BlueprintGenerator\UE5BlueprintGenerator.uplugin"
   ```

### Using Visual Studio

1. **Open UE5.sln** in Visual Studio
2. **Locate UE5BlueprintGenerator** in Solution Explorer
3. **Right-click** → **Build**
4. **Verify compilation** completes without errors

## Verification

### Check Plugin Compilation
1. **Look for compiled binaries**:
   ```
   C:\Program Files\Epic Games\UE_5.4\Engine\Binaries\Win64\
   ```
2. **Verify plugin DLL** exists:
   ```
   UE5BlueprintGenerator-Win64-Shipping.dll
   ```

### Test Plugin in Editor
1. **Launch UE5.4 Editor**
2. **Create new project** or **open existing project**
3. **Go to** Edit → Plugins
4. **Search for** "UE5 Blueprint Generator"
5. **Enable the plugin**
6. **Restart editor** when prompted
7. **Check Tools menu** for "Blueprint Generator" option

## Configuration

### AI Model Setup
1. **Install Ollama** or **LMStudio**:
   - **Ollama**: Download from https://ollama.ai/
   - **LMStudio**: Download from https://lmstudio.ai/

2. **Configure plugin**:
   - Open **Tools** → **Blueprint Generator**
   - Click **Settings**
   - Configure AI model provider and endpoint

### Plugin Settings
Access plugin settings via:
- **Edit** → **Project Settings** → **Plugins** → **UE5 Blueprint Generator**

## Troubleshooting

### Common Issues

#### 1. Compilation Errors
**Problem**: Missing dependencies or headers
**Solution**: 
- Verify all UE5.4 development components are installed
- Check Visual Studio C++ tools are installed
- Regenerate project files

#### 2. Plugin Not Appearing
**Problem**: Plugin doesn't show in Plugin Manager
**Solution**:
- Verify plugin files are in correct location
- Check UE5BlueprintGenerator.uplugin syntax
- Restart UE5 Editor

#### 3. Build Failures
**Problem**: Build fails with linker errors
**Solution**:
- Clean and rebuild solution
- Check Windows SDK version compatibility
- Verify UE5.4 installation integrity

#### 4. Permission Issues
**Problem**: Cannot copy files to Program Files
**Solution**:
- Run Command Prompt as Administrator
- Use elevated permissions for file operations

### Build Configuration

For optimal performance, use these build configurations:

- **Development**: For testing and debugging
- **Shipping**: For production use
- **Debug**: For detailed debugging (slower)

### Performance Optimization

1. **Enable plugin caching** in settings
2. **Configure AI model** for optimal response times
3. **Use SSD storage** for better I/O performance

## Usage

### Basic Workflow
1. **Open Blueprint Generator** from Tools menu
2. **Enter natural language description**:
   ```
   "Create a player character that can jump and move with WASD keys"
   ```
3. **Select blueprint type** (Actor, Component, etc.)
4. **Configure generation options**
5. **Click Generate Blueprint**
6. **Review and modify** generated blueprint

### Advanced Features
- **Custom templates** for code generation
- **Batch blueprint generation**
- **Export to C++ code**
- **Integration with version control**

## Support

### Documentation
- Check `docs/` folder for detailed documentation
- Review `CHANGELOG.md` for feature updates
- See `PROJECT_PLAN.md` for development roadmap

### Debugging
- Enable verbose logging in plugin settings
- Check UE5 Output Log for error messages
- Use Visual Studio debugger for development

## Next Steps

1. **Test basic functionality** with simple blueprint generation
2. **Configure AI model** for your specific needs
3. **Explore advanced features** like code export
4. **Integrate with your development workflow**

---

**Plugin Version**: 1.0.0  
**UE5 Compatibility**: 5.1+  
**Last Updated**: December 2024 