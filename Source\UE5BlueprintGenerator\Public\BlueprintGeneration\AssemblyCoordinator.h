#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "BlueprintGeneration/BlueprintGenerationTypes.h"
#include "Templates/SharedPointer.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "HAL/CriticalSection.h"
#include "Misc/DateTime.h"
#include "AssemblyCoordinator.generated.h"

// Forward declarations
class UBlueprint;
class UEdGraph;
class UK2Node;
class UEdGraphPin;
class FBlueprintAssembler;
class FAssemblyValidator;
class FNodeAssemblyEngine;
class FConnectionAssemblySystem;
class FPropertyAssemblyManager;
class FNodePositionCalculator;
class FOptimizationEngine;
class FCompilationManager;

DECLARE_LOG_CATEGORY_EXTERN(LogAssemblyCoordinator, Log, All);

/**
 * Assembly phase enumeration for tracking assembly progress
 */
UENUM(BlueprintType)
enum class EAssemblyPhase : uint8
{
    None,
    Initialization,
    StructureAnalysis,
    DependencyResolution,
    NodeCreation,
    PropertyConfiguration,
    PositionCalculation,
    ConnectionEstablishment,
    Validation,
    Optimization,
    Finalization,
    Complete,
    Failed
};

/**
 * Assembly request structure containing all information needed for blueprint assembly
 */
USTRUCT(BlueprintType)
struct FASSEMBLYREQUEST
{
    GENERATED_BODY()

    // Blueprint creation request
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FGENERALBLUEPRINTCREATIONREQUEST BlueprintRequest;

    // Extracted blueprint structure from NLP analysis
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FExtractedBlueprintStructure ExtractedStructure;

    // Dependency analysis results
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FDependencyAnalysisResult DependencyAnalysis;

    // Complexity estimation results
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FCOMPLEXITYESTIMATIONRESULT ComplexityEstimation;

    // Assembly configuration
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    FASSEMBLYCONFIG Config;

    // Additional context information
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Request")
    TMap<FString, FString> ContextData;

    FASSEMBLYREQUEST()
    {
        // Initialize with default values
    }
};

/**
 * Assembly execution plan structure for organizing assembly operations
 */
USTRUCT(BlueprintType)
struct FASSEMBLYEXECUTIONPLAN
{
    GENERATED_BODY()

    // Plan identifier
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    FString PlanId;

    // Assembly strategy to use
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    EAssemblyStrategy Strategy = EAssemblyStrategy::Sequential;

    // Ordered list of assembly tasks
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    TArray<FASSEMBLYTASK> Tasks;

    // Task dependencies mapping
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    TMap<FString, FString> TaskDependencies;

    // Estimated total execution time
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    float EstimatedExecutionTime = 0.0f;

    // Plan creation timestamp
    UPROPERTY(BlueprintReadOnly, Category = "Plan")
    FDateTime CreationTime;

    FASSEMBLYEXECUTIONPLAN()
    {
        Strategy = EAssemblyStrategy::Sequential;
        EstimatedExecutionTime = 0.0f;
        CreationTime = FDateTime::Now();
    }
};

/**
 * Assembly execution context structure for maintaining state during assembly
 */
USTRUCT(BlueprintType)
struct FASSEMBLYEXECUTIONCONTEXT
{
    GENERATED_BODY()

    // Target blueprint being assembled
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UBlueprint> TargetBlueprint = nullptr;

    // Target graph for node creation
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UEdGraph> TargetGraph = nullptr;

    // Created nodes mapping (NodeId -> Node)
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, TObjectPtr<UK2Node>> CreatedNodes;

    // Assembly configuration
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FASSEMBLYCONFIG Config;

    // Current assembly phase
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    EAssemblyPhase CurrentPhase = EAssemblyPhase::None;

    // Assembly start time
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FDateTime StartTime;

    // Context data for sharing between assembly components
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, FString> ContextData;

    FASSEMBLYEXECUTIONCONTEXT()
    {
        TargetBlueprint = nullptr;
        TargetGraph = nullptr;
        CurrentPhase = EAssemblyPhase::None;
        StartTime = FDateTime::Now();
    }
};

/**
 * Assembly statistics structure for performance monitoring
 */
USTRUCT(BlueprintType)
struct FASSEMBLYSTATISTICS
{
    GENERATED_BODY()

    // Total number of assemblies performed
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalAssemblies = 0;

    // Number of successful assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 SuccessfulAssemblies = 0;

    // Number of failed assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 FailedAssemblies = 0;

    // Average assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageAssemblyTime = 0.0f;

    // Total assembly time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float TotalAssemblyTime = 0.0f;

    // Average nodes created per assembly
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageNodesCreated = 0.0f;

    // Total nodes created across all assemblies
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalNodesCreated = 0;

    // Performance metrics by phase
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EAssemblyPhase, float> PhasePerformanceMetrics;

    FASSEMBLYSTATISTICS()
    {
        TotalAssemblies = 0;
        SuccessfulAssemblies = 0;
        FailedAssemblies = 0;
        AverageAssemblyTime = 0.0f;
        TotalAssemblyTime = 0.0f;
        AverageNodesCreated = 0.0f;
        TotalNodesCreated = 0;
    }
};

/**
 * Delegate declarations for assembly events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyPhaseChanged, EAssemblyPhase, NewPhase);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyProgressUpdated, const FASSEMBLYPROGRESS&, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyTaskCompleted, const FASSEMBLYTASK&, Task);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnAssemblyCompleted, const FGENERALASSEMBLYEXECUTIONRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAssemblyError, const FString&, ErrorMessage, EAssemblyPhase, Phase);

/**
 * Assembly Coordinator - Orchestrates the entire blueprint assembly process
 *
 * This class coordinates all the components we've built to assemble complete blueprints
 * from natural language descriptions. It manages the assembly pipeline, handles
 * dependencies, monitors progress, and ensures quality through validation.
 */
class UE5BLUEPRINTGENERATOR_API FAssemblyCoordinator
{
public:
    FAssemblyCoordinator();
    virtual ~FAssemblyCoordinator();

    // Singleton access
    static FAssemblyCoordinator& Get();

    // Core assembly operations
    bool AssembleBlueprint(const FAssemblyRequest& Request, FAssemblyResult& OutResult);
    bool IsAssemblyInProgress() const;
    void CancelAssembly();

    // Progress monitoring
    FAssemblyProgress GetCurrentProgress() const;
    TArray<FAssemblyTask> GetCurrentTasks() const;
    EAssemblyPhase GetCurrentPhase() const;

    // Configuration
    void SetAssemblyConfig(const FAssemblyConfig& Config);
    FAssemblyConfig GetAssemblyConfig() const;

    // Statistics
    FAssemblyStatistics GetStatistics() const;
    void ResetStatistics();

    // Event delegates
    FOnAssemblyPhaseChanged OnAssemblyPhaseChanged;
    FOnAssemblyProgressUpdated OnAssemblyProgressUpdated;
    FOnAssemblyTaskCompleted OnAssemblyTaskCompleted;
    FOnAssemblyCompleted OnAssemblyCompleted;
    FOnAssemblyError OnAssemblyError;

private:
    // Component references
    TSharedPtr<FBlueprintAssembler> BlueprintAssembler;
    TSharedPtr<FAssemblyValidator> AssemblyValidator;
    TSharedPtr<FNodeAssemblyEngine> NodeAssemblyEngine;
    TSharedPtr<FConnectionAssemblySystem> ConnectionAssemblySystem;
    TSharedPtr<FPropertyAssemblyManager> PropertyAssemblyManager;
    TSharedPtr<FNodePositionCalculator> NodePositionCalculator;
    TSharedPtr<FOptimizationEngine> OptimizationEngine;
    TSharedPtr<FCompilationManager> CompilationManager;

    // Current assembly state
    bool bIsAssemblyInProgress;
    FAssemblyRequest CurrentRequest;
    FAssemblyResult CurrentResult;
    FAssemblyExecutionPlan CurrentPlan;
    FAssemblyExecutionContext CurrentContext;
    FAssemblyProgress CurrentProgress;
    TArray<FAssemblyTask> CurrentTasks;
    EAssemblyPhase CurrentPhase;

    // Configuration and statistics
    FAssemblyConfig Config;
    FAssemblyStatistics Statistics;

    // Thread safety
    mutable FCriticalSection AssemblyMutex;

    // Assembly pipeline methods
    bool InitializeAssembly(const FAssemblyRequest& Request);
    bool CreateBlueprintAsset();
    bool AnalyzeStructure();
    bool ResolveDependencies();
    bool CreateNodes();
    bool ConfigureProperties();
    bool CalculatePositions();
    bool EstablishConnections();
    bool ValidateBlueprint();
    bool FinalizeAssembly();

    // Task management
    void CreateAssemblyTasks();
    void UpdateTaskProgress(const FString& TaskId, bool bCompleted, bool bFailed = false, const FString& ErrorMessage = TEXT(""));
    void UpdateOverallProgress();
    FAssemblyTask* FindTask(const FString& TaskId);

    // Strategy execution
    bool ExecuteSequentialStrategy();
    bool ExecuteParallelStrategy();
    bool ExecuteHierarchicalStrategy();
    bool ExecuteOptimizedStrategy();
    bool ExecuteCustomStrategy();

    // Error handling
    bool HandleAssemblyError(const FString& ErrorMessage, EAssemblyPhase Phase);
    bool AttemptErrorRecovery();
    void CleanupFailedAssembly();

    // Performance monitoring
    void StartPerformanceMonitoring();
    void UpdatePerformanceMetrics();
    void StopPerformanceMonitoring();

    // Event broadcasting
    void LogAssemblyProgress(const FString& Message);
    void BroadcastPhaseChange(EAssemblyPhase NewPhase);
    void BroadcastProgressUpdate();
    void BroadcastTaskCompletion(const FAssemblyTask& Task);
    void BroadcastAssemblyCompletion();
    void BroadcastAssemblyError(const FString& ErrorMessage, EAssemblyPhase Phase);

    // Initialization and cleanup
    void InitializeComponents();
    void ShutdownComponents();

    // Validation helpers
    bool ValidateAssemblyRequest(const FAssemblyRequest& Request);
    bool ValidateAssemblyConfig(const FAssemblyConfig& Config);

    // Statistics helpers
    void UpdateStatistics(const FAssemblyResult& Result);
    void CalculatePerformanceMetrics();
};