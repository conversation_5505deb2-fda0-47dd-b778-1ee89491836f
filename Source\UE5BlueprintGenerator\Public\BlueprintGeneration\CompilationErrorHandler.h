#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "BlueprintGeneration/CompilationManager.h"
#include "BlueprintGeneration/BlueprintGenerationTypes.h"

DECLARE_LOG_CATEGORY_EXTERN(LogCompilationErrorHandler, Log, All);

/**
 * Error classification enumeration
 */
UENUM(BlueprintType)
enum class ECompilationErrorClass : uint8
{
    Unknown,                // Unknown error type
    Syntax,                 // Syntax errors
    Semantic,               // Semantic errors
    Type,                   // Type mismatch errors
    Connection,             // Connection errors
    Property,               // Property configuration errors
    Dependency,             // Dependency errors
    Performance,            // Performance-related errors
    Validation,             // Validation errors
    Runtime,                // Runtime errors
    Memory,                 // Memory-related errors
    Network,                // Network/replication errors
    Custom                  // Custom error types
};

/**
 * Error recovery strategy enumeration
 */
UENUM(BlueprintType)
enum class EErrorRecoveryStrategy : uint8
{
    None,                   // No recovery strategy
    AutoFix,                // Automatic fix attempt
    UserPrompt,             // Prompt user for action
    Fallback,               // Use fallback implementation
    Skip,                   // Skip problematic element
    Retry,                  // Retry operation
    Rollback,               // Rollback to previous state
    Alternative,            // Use alternative approach
    Custom                  // Custom recovery strategy
};

/**
 * Error handling mode enumeration
 */
UENUM(BlueprintType)
enum class EErrorHandlingMode : uint8
{
    Strict,                 // Strict error handling (fail on any error)
    Tolerant,               // Tolerant error handling (continue with warnings)
    Aggressive,             // Aggressive auto-fixing
    Interactive,            // Interactive error resolution
    Silent,                 // Silent error handling
    Custom                  // Custom error handling mode
};

/**
 * Error context structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONERRORCONTEXT
{
    GENERATED_BODY()

    // Source blueprint
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UBlueprint> Blueprint = nullptr;

    // Source graph
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UEdGraph> Graph = nullptr;

    // Source node
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TObjectPtr<UK2Node> Node = nullptr;

    // Compilation phase
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString CompilationPhase;

    // Error location
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString ErrorLocation;

    // Context data
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, FString> ContextData;

    // Stack trace
    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TArray<FString> StackTrace;

    FCOMPILATIONERRORCONTEXT()
    {
        CompilationPhase = TEXT("Unknown");
        ErrorLocation = TEXT("Unknown");
    }
};

/**
 * Error analysis result structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONERRORANALYSISRESULT
{
    GENERATED_BODY()

    // Error classification
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    ECompilationErrorClass ErrorClass = ECompilationErrorClass::Unknown;

    // Error confidence (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float ClassificationConfidence = 0.0f;

    // Root cause analysis
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    FString RootCause;

    // Contributing factors
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> ContributingFactors;

    // Recommended recovery strategy
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    EErrorRecoveryStrategy RecommendedStrategy = EErrorRecoveryStrategy::None;

    // Alternative strategies
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<EErrorRecoveryStrategy> AlternativeStrategies;

    // Auto-fix feasibility
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    bool bCanAutoFix = false;

    // Auto-fix confidence (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float AutoFixConfidence = 0.0f;

    // Suggested fixes
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> SuggestedFixes;

    // Prevention suggestions
    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> PreventionSuggestions;

    FCOMPILATIONERRORANALYSISRESULT()
    {
        ErrorClass = ECompilationErrorClass::Unknown;
        ClassificationConfidence = 0.0f;
        RecommendedStrategy = EErrorRecoveryStrategy::None;
        bCanAutoFix = false;
        AutoFixConfidence = 0.0f;
    }
};

/**
 * Error recovery result structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONERRORRECOVERYRESULT
{
    GENERATED_BODY()

    // Recovery success
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    bool bRecoverySuccessful = false;

    // Strategy used
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    EErrorRecoveryStrategy StrategyUsed = EErrorRecoveryStrategy::None;

    // Recovery description
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    FString RecoveryDescription;

    // Recovery time (seconds)
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    float RecoveryTime = 0.0f;

    // Changes made
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    TArray<FString> ChangesMade;

    // Side effects
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    TArray<FString> SideEffects;

    // Rollback information
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    FString RollbackInfo;

    // Recovery confidence (0.0-1.0)
    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    float RecoveryConfidence = 0.0f;

    FCOMPILATIONERRORRECOVERYRESULT()
    {
        bRecoverySuccessful = false;
        StrategyUsed = EErrorRecoveryStrategy::None;
        RecoveryTime = 0.0f;
        RecoveryConfidence = 0.0f;
    }
};

/**
 * Error handling configuration structure
 */
USTRUCT(BlueprintType)
struct FERRORHANDLINGCONFIG
{
    GENERATED_BODY()

    // Error handling mode
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    EErrorHandlingMode HandlingMode = EErrorHandlingMode::Tolerant;

    // Enable auto-fix
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableAutoFix = true;

    // Enable error recovery
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorRecovery = true;

    // Enable error analysis
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorAnalysis = true;

    // Enable error logging
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorLogging = true;

    // Enable error statistics
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Error Handling")
    bool bEnableErrorStatistics = true;

    // Maximum auto-fix attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto-Fix", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxAutoFixAttempts = 3;

    // Auto-fix confidence threshold (0.0-1.0)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Auto-Fix", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float AutoFixConfidenceThreshold = 0.7f;

    // Maximum recovery attempts
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery", meta = (ClampMin = "0", ClampMax = "10"))
    int32 MaxRecoveryAttempts = 5;

    // Recovery timeout (seconds)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Recovery", meta = (ClampMin = "1", ClampMax = "300"))
    float RecoveryTimeout = 30.0f;

    // Error severity threshold
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    ECompilationErrorSeverity SeverityThreshold = ECompilationErrorSeverity::Warning;

    // Ignored error classes
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Filtering")
    TArray<ECompilationErrorClass> IgnoredErrorClasses;

    // Custom error patterns
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
    TMap<FString, FString> CustomErrorPatterns;

    // Custom recovery strategies
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Custom")
    TMap<FString, FString> CustomRecoveryStrategies;

    FERRORHANDLINGCONFIG()
    {
        HandlingMode = EErrorHandlingMode::Tolerant;
        bEnableAutoFix = true;
        bEnableErrorRecovery = true;
        bEnableErrorAnalysis = true;
        bEnableErrorLogging = true;
        bEnableErrorStatistics = true;
        MaxAutoFixAttempts = 3;
        AutoFixConfidenceThreshold = 0.7f;
        MaxRecoveryAttempts = 5;
        RecoveryTimeout = 30.0f;
        SeverityThreshold = ECompilationErrorSeverity::Warning;
    }
};

/**
 * Error handling statistics structure
 */
USTRUCT(BlueprintType)
struct FERRORHANDLINGSTATISTICS
{
    GENERATED_BODY()

    // Total errors handled
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 TotalErrorsHandled = 0;

    // Errors by class
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ECompilationErrorClass, int32> ErrorsByClass;

    // Errors by severity
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<ECompilationErrorSeverity, int32> ErrorsBySeverity;

    // Auto-fixes attempted
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 AutoFixesAttempted = 0;

    // Auto-fixes successful
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 AutoFixesSuccessful = 0;

    // Recovery attempts
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 RecoveryAttempts = 0;

    // Recovery successes
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    int32 RecoverySuccesses = 0;

    // Average recovery time
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    float AverageRecoveryTime = 0.0f;

    // Strategy usage
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<EErrorRecoveryStrategy, int32> StrategyUsage;

    // Most common errors
    UPROPERTY(BlueprintReadOnly, Category = "Statistics")
    TMap<FString, int32> MostCommonErrors;

    FERRORHANDLINGSTATISTICS()
    {
        TotalErrorsHandled = 0;
        AutoFixesAttempted = 0;
        AutoFixesSuccessful = 0;
        RecoveryAttempts = 0;
        RecoverySuccesses = 0;
        AverageRecoveryTime = 0.0f;
    }
};

/**
 * Delegate declarations for error handling events
 */
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnErrorDetected, const FCOMPILATIONMANAGERERROR&, Error, const FCOMPILATIONERRORCONTEXT&, Context);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnErrorAnalyzed, const FCOMPILATIONMANAGERERROR&, Error, const FCOMPILATIONERRORANALYSISRESULT&, Analysis);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAutoFixAttempted, const FCOMPILATIONMANAGERERROR&, Error, const FString&, FixDescription);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnAutoFixCompleted, const FCOMPILATIONMANAGERERROR&, Error, const FCOMPILATIONERRORRECOVERYRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRecoveryAttempted, const FCOMPILATIONMANAGERERROR&, Error, EErrorRecoveryStrategy, Strategy);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnRecoveryCompleted, const FCOMPILATIONMANAGERERROR&, Error, const FCOMPILATIONERRORRECOVERYRESULT&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnErrorResolved, const FCOMPILATIONMANAGERERROR&, Error);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnErrorUnresolved, const FCOMPILATIONMANAGERERROR&, Error);

/**
 * Compilation Error Handler - Specialized error handling and recovery system
 * 
 * This class provides comprehensive error handling capabilities for blueprint
 * compilation, including error classification, analysis, auto-fixing, and
 * recovery strategies. It ensures robust compilation with intelligent error
 * resolution and detailed error reporting.
 */
class UE5BLUEPRINTGENERATOR_API FCompilationErrorHandler
{
public:
    FCompilationErrorHandler();
    virtual ~FCompilationErrorHandler();

    // Core error handling operations
    bool HandleError(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context, FCOMPILATIONERRORRECOVERYRESULT& OutResult);
    bool HandleErrors(const TArray<FCOMPILATIONMANAGERERROR>& Errors, const FCOMPILATIONERRORCONTEXT& Context, TArray<FCOMPILATIONERRORRECOVERYRESULT>& OutResults);

    // Error analysis
    FCOMPILATIONERRORANALYSISRESULT AnalyzeError(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);
    ECompilationErrorClass ClassifyError(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);
    float CalculateClassificationConfidence(const FCOMPILATIONMANAGERERROR& Error, ECompilationErrorClass ErrorClass);

    // Auto-fix operations
    bool CanAutoFix(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);
    bool AttemptAutoFix(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context, FCOMPILATIONERRORRECOVERYRESULT& OutResult);
    TArray<FString> GetAutoFixSuggestions(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);

    // Recovery operations
    bool AttemptRecovery(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context, EErrorRecoveryStrategy Strategy, FCOMPILATIONERRORRECOVERYRESULT& OutResult);
    EErrorRecoveryStrategy SelectRecoveryStrategy(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);
    TArray<EErrorRecoveryStrategy> GetAvailableStrategies(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);

    // Error prevention
    TArray<FString> GetPreventionSuggestions(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);
    bool CanPreventError(const FCOMPILATIONMANAGERERROR& Error, const FCOMPILATIONERRORCONTEXT& Context);

    // Configuration management
    void SetErrorHandlingConfig(const FErrorHandlingConfig& Config);
    FErrorHandlingConfig GetErrorHandlingConfig() const;

    // Statistics and monitoring
    FErrorHandlingStatistics GetStatistics() const;
    void ResetStatistics();

    // Error pattern management
    void AddCustomErrorPattern(const FString& Pattern, const FString& Description);
    void RemoveCustomErrorPattern(const FString& Pattern);
    bool MatchesCustomPattern(const FCompilationError& Error, FString& OutPattern);

    // Recovery strategy management
    void RegisterCustomRecoveryStrategy(const FString& StrategyName, TFunction<bool(const FCompilationError&, const FErrorContext&, FErrorRecoveryResult&)> StrategyFunction);
    void UnregisterCustomRecoveryStrategy(const FString& StrategyName);

    // Event delegates
    FOnErrorDetected OnErrorDetected;
    FOnErrorAnalyzed OnErrorAnalyzed;
    FOnAutoFixAttempted OnAutoFixAttempted;
    FOnAutoFixCompleted OnAutoFixCompleted;
    FOnRecoveryAttempted OnRecoveryAttempted;
    FOnRecoveryCompleted OnRecoveryCompleted;
    FOnErrorResolved OnErrorResolved;
    FOnErrorUnresolved OnErrorUnresolved;

private:
    // Configuration
    FErrorHandlingConfig Config;

    // Statistics
    FErrorHandlingStatistics Statistics;

    // Error classification patterns
    TMap<ECompilationErrorClass, TArray<FString>> ClassificationPatterns;

    // Auto-fix handlers
    TMap<ECompilationErrorClass, TFunction<bool(const FCompilationError&, const FErrorContext&, FErrorRecoveryResult&)>> AutoFixHandlers;

    // Recovery strategy handlers
    TMap<EErrorRecoveryStrategy, TFunction<bool(const FCompilationError&, const FErrorContext&, FErrorRecoveryResult&)>> RecoveryHandlers;

    // Custom recovery strategies
    TMap<FString, TFunction<bool(const FCompilationError&, const FErrorContext&, FErrorRecoveryResult&)>> CustomRecoveryStrategies;

    // Error analysis methods
    FString AnalyzeRootCause(const FCompilationError& Error, const FErrorContext& Context);
    TArray<FString> IdentifyContributingFactors(const FCompilationError& Error, const FErrorContext& Context);
    float CalculateAutoFixConfidence(const FCompilationError& Error, const FErrorContext& Context);

    // Classification methods
    bool MatchesClassificationPattern(const FCompilationError& Error, ECompilationErrorClass ErrorClass);
    ECompilationErrorClass ClassifyBySyntax(const FCompilationError& Error);
    ECompilationErrorClass ClassifyBySemantic(const FCompilationError& Error);
    ECompilationErrorClass ClassifyByContext(const FCompilationError& Error, const FErrorContext& Context);

    // Auto-fix implementation methods
    bool AutoFixSyntaxError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool AutoFixSemanticError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool AutoFixTypeError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool AutoFixConnectionError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool AutoFixPropertyError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool AutoFixDependencyError(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);

    // Recovery strategy implementation methods
    bool ExecuteAutoFixStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool ExecuteUserPromptStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool ExecuteFallbackStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool ExecuteSkipStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool ExecuteRetryStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool ExecuteRollbackStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);
    bool ExecuteAlternativeStrategy(const FCompilationError& Error, const FErrorContext& Context, FErrorRecoveryResult& OutResult);

    // Utility methods
    FErrorContext CreateErrorContext(UBlueprint* Blueprint, UEdGraph* Graph, UK2Node* Node, const FString& Phase);
    bool ShouldIgnoreError(const FCompilationError& Error);
    bool IsErrorRecoverable(const FCompilationError& Error, const FErrorContext& Context);
    void UpdateStatistics(const FCompilationError& Error, const FErrorRecoveryResult& Result);

    // Event broadcasting
    void BroadcastErrorDetected(const FCompilationError& Error, const FErrorContext& Context);
    void BroadcastErrorAnalyzed(const FCompilationError& Error, const FErrorAnalysisResult& Analysis);
    void BroadcastAutoFixAttempted(const FCompilationError& Error, const FString& FixDescription);
    void BroadcastAutoFixCompleted(const FCompilationError& Error, const FErrorRecoveryResult& Result);
    void BroadcastRecoveryAttempted(const FCompilationError& Error, EErrorRecoveryStrategy Strategy);
    void BroadcastRecoveryCompleted(const FCompilationError& Error, const FErrorRecoveryResult& Result);
    void BroadcastErrorResolved(const FCompilationError& Error);
    void BroadcastErrorUnresolved(const FCompilationError& Error);

    // Component initialization
    void InitializeComponents();
    void InitializeClassificationPatterns();
    void InitializeAutoFixHandlers();
    void InitializeRecoveryHandlers();
    void ShutdownComponents();
}; 