#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"

DECLARE_LOG_CATEGORY_EXTERN(LogBlueprintAssetCreator, Log, All);

/**
 * Enumeration for different types of blueprints that can be created
 */
UENUM(BlueprintType)
enum class EBlueprintGeneratorType : uint8
{
    Actor           UMETA(DisplayName = "Actor Blueprint"),
    Component       UMETA(DisplayName = "Component Blueprint"),
    FunctionLibrary UMETA(DisplayName = "Function Library"),
    Interface       UMETA(DisplayName = "Blueprint Interface"),
    MacroLibrary    UMETA(DisplayName = "Macro Library"),
    AnimBlueprint   UMETA(DisplayName = "Animation Blueprint"),
    WidgetBlueprint UMETA(DisplayName = "Widget Blueprint"),
    GameMode        UMETA(DisplayName = "Game Mode Blueprint"),
    PlayerController UMETA(DisplayName = "Player Controller Blueprint"),
    AIController    UMETA(DisplayName = "AI Controller Blueprint")
};

/**
 * Structure containing information about a blueprint creation request
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTCREATIONREQUEST
{
    GENERATED_BODY()

    /** Name of the blueprint to create */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    FString BlueprintName;

    /** Type of blueprint to create */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    EBlueprintGeneratorType BlueprintType;

    /** Parent class for the blueprint */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    UClass* ParentClass;

    /** Folder path where the blueprint should be saved */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    FString FolderPath;

    /** Description of the blueprint functionality */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    FString Description;

    /** Whether to generate comments for the blueprint */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    bool bGenerateComments;

    /** Whether to include error handling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    bool bIncludeErrorHandling;

    /** Whether to optimize for performance */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    bool bOptimizePerformance;

    FBLUEPRINTCREATIONREQUEST()
        : BlueprintName(TEXT(""))
        , BlueprintType(EBlueprintGeneratorType::Actor)
        , ParentClass(nullptr)
        , FolderPath(TEXT("/Game/Blueprints"))
        , Description(TEXT(""))
        , bGenerateComments(true)
        , bIncludeErrorHandling(true)
        , bOptimizePerformance(true)
    {
    }
};

/**
 * Structure containing the result of a blueprint creation operation
 */
USTRUCT(BlueprintType)
struct FBLUEPRINTCREATIONRESULT
{
    GENERATED_BODY()

    /** Whether the creation was successful */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    bool bSuccess;

    /** The created blueprint (if successful) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    UBlueprint* CreatedBlueprint;

    /** Error message (if creation failed) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    FString ErrorMessage;

    /** Warning messages */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    TArray<FString> WarningMessages;

    /** Full path to the created asset */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Blueprint Creation")
    FString AssetPath;

    FBLUEPRINTCREATIONRESULT()
        : bSuccess(false)
        , CreatedBlueprint(nullptr)
        , ErrorMessage(TEXT(""))
        , AssetPath(TEXT(""))
    {
    }
};

/**
 * Class responsible for creating UE5 blueprint assets from AI-generated specifications
 */
class UE5BLUEPRINTGENERATOR_API FBlueprintAssetCreator
{
public:
    FBlueprintAssetCreator();
    ~FBlueprintAssetCreator();

    // Asset creation methods
    
    /**
     * Creates a blueprint asset based on the provided request
     * @param Request - The blueprint creation request containing all necessary information
     * @return Result of the creation operation
     */
    FBLUEPRINTCREATIONRESULT CreateBlueprintAsset(const FBLUEPRINTCREATIONREQUEST& Request);

    /**
     * Creates a specific type of blueprint with default settings
     * @param BlueprintName - Name of the blueprint
     * @param BlueprintType - Type of blueprint to create
     * @param FolderPath - Path where the blueprint should be saved
     * @return The created blueprint or nullptr if creation failed
     */
    UBlueprint* CreateBlueprintOfType(const FString& BlueprintName, EBlueprintGeneratorType BlueprintType, const FString& FolderPath);

    /**
     * Creates an Actor blueprint
     * @param BlueprintName - Name of the blueprint
     * @param ParentClass - Parent class (defaults to AActor)
     * @param FolderPath - Path where the blueprint should be saved
     * @return The created blueprint or nullptr if creation failed
     */
    UBlueprint* CreateActorBlueprint(const FString& BlueprintName, UClass* ParentClass = nullptr, const FString& FolderPath = TEXT("/Game/Blueprints"));

    /**
     * Creates a Component blueprint
     * @param BlueprintName - Name of the blueprint
     * @param ParentClass - Parent class (defaults to UActorComponent)
     * @param FolderPath - Path where the blueprint should be saved
     * @return The created blueprint or nullptr if creation failed
     */
    UBlueprint* CreateComponentBlueprint(const FString& BlueprintName, UClass* ParentClass = nullptr, const FString& FolderPath = TEXT("/Game/Blueprints"));

    /**
     * Creates a Function Library blueprint
     * @param BlueprintName - Name of the blueprint
     * @param FolderPath - Path where the blueprint should be saved
     * @return The created blueprint or nullptr if creation failed
     */
    UBlueprint* CreateFunctionLibraryBlueprint(const FString& BlueprintName, const FString& FolderPath = TEXT("/Game/Blueprints"));

    /**
     * Creates a Blueprint Interface
     * @param BlueprintName - Name of the blueprint
     * @param FolderPath - Path where the blueprint should be saved
     * @return The created blueprint or nullptr if creation failed
     */
    UBlueprint* CreateInterfaceBlueprint(const FString& BlueprintName, const FString& FolderPath = TEXT("/Game/Blueprints"));

    // Asset management methods

    /**
     * Saves a blueprint asset to disk
     * @param Blueprint - The blueprint to save
     * @return True if save was successful
     */
    bool SaveBlueprintAsset(UBlueprint* Blueprint);

    /**
     * Compiles a blueprint asset
     * @param Blueprint - The blueprint to compile
     * @return True if compilation was successful
     */
    bool CompileBlueprintAsset(UBlueprint* Blueprint);

    /**
     * Deletes a blueprint asset
     * @param Blueprint - The blueprint to delete
     * @return True if deletion was successful
     */
    bool DeleteBlueprintAsset(UBlueprint* Blueprint);

    /**
     * Refreshes the content browser to show newly created assets
     */
    void RefreshContentBrowser();

    // Asset validation methods

    /**
     * Validates if a blueprint name is valid according to UE5 naming conventions
     * @param BlueprintName - The name to validate
     * @return True if the name is valid
     */
    bool IsBlueprintNameValid(const FString& BlueprintName);

    /**
     * Checks if a blueprint with the given name already exists in the specified folder
     * @param BlueprintName - The name to check
     * @param FolderPath - The folder path to check in
     * @return True if a blueprint with this name already exists
     */
    bool DoesBlueprintExist(const FString& BlueprintName, const FString& FolderPath);

    /**
     * Generates a unique blueprint name if the requested name already exists
     * @param BaseName - The base name to use
     * @param FolderPath - The folder path to check in
     * @return A unique blueprint name
     */
    FString GenerateUniqueBlueprintName(const FString& BaseName, const FString& FolderPath);

    // Utility methods

    /**
     * Gets the default parent class for a given blueprint type
     * @param BlueprintType - The type of blueprint
     * @return The default parent class for this type
     */
    UClass* GetDefaultParentClass(EBlueprintGeneratorType BlueprintType);

    /**
     * Gets the blueprint factory class for a given blueprint type
     * @param BlueprintType - The type of blueprint
     * @return The factory class for creating this type of blueprint
     */
    UClass* GetBlueprintFactoryClass(EBlueprintGeneratorType BlueprintType);

    /**
     * Converts a blueprint type enum to a human-readable string
     * @param BlueprintType - The blueprint type to convert
     * @return String representation of the blueprint type
     */
    FString BlueprintTypeToString(EBlueprintGeneratorType BlueprintType);

    /**
     * Converts a string to a blueprint type enum
     * @param TypeString - The string to convert
     * @return The corresponding blueprint type
     */
    EBlueprintGeneratorType StringToBlueprintType(const FString& TypeString);

private:
    // Helper methods

    /**
     * Sanitizes a blueprint name to ensure it follows UE5 naming conventions
     * @param BlueprintName - The name to sanitize
     * @return The sanitized name
     */
    FString SanitizeBlueprintName(const FString& BlueprintName);

    /**
     * Creates a package for the blueprint asset
     * @param BlueprintName - Name of the blueprint
     * @param FolderPath - Path where the package should be created
     * @return The created package or nullptr if creation failed
     */
    UPackage* CreatePackage(const FString& BlueprintName, const FString& FolderPath);

    /**
     * Sets up default properties for a newly created blueprint
     * @param Blueprint - The blueprint to configure
     * @param BlueprintType - The type of blueprint being created
     */
    void SetupBlueprintDefaults(UBlueprint* Blueprint, EBlueprintGeneratorType BlueprintType);

    /**
     * Validates the creation request before attempting to create the blueprint
     * @param Request - The creation request to validate
     * @param OutErrorMessage - Error message if validation fails
     * @return True if the request is valid
     */
    bool ValidateCreationRequest(const FBLUEPRINTCREATIONREQUEST& Request, FString& OutErrorMessage);

    /**
     * Shows a notification to the user about the blueprint creation result
     * @param Message - The message to display
     * @param bIsSuccess - Whether this is a success or error notification
     */
    void ShowNotification(const FString& Message, bool bIsSuccess);

    /**
     * Logs blueprint creation activity
     * @param Message - The message to log
     * @param bIsError - Whether this is an error message
     */
    void LogBlueprintCreation(const FString& Message, bool bIsError = false);

    // Member variables

    /** Map of blueprint types to their default parent classes */
    TMap<EBlueprintGeneratorType, UClass*> DefaultParentClasses;

    /** Map of blueprint types to their factory classes */
    TMap<EBlueprintGeneratorType, UClass*> BlueprintFactoryClasses;

    /** Counter for generating unique names */
    int32 UniqueNameCounter;

    /** Whether to show notifications for blueprint creation */
    bool bShowNotifications;

    /** Whether to automatically save created blueprints */
    bool bAutoSave;

    /** Whether to automatically compile created blueprints */
    bool bAutoCompile;
}; 