# UE5 Blueprint Plugin Generator - Installation Script
# This script automates the installation of the plugin to UE5.4

param(
    [string]$InstallMethod = "Engine",  # "Engine" or "Project"
    [string]$ProjectPath = "",          # Required if InstallMethod is "Project"
    [switch]$Force = $false             # Force overwrite existing installation
)

# Configuration
$UE5Path = "C:\Program Files\Epic Games\UE_5.4"
$PluginName = "UE5BlueprintGenerator"
$SourcePath = Get-Location
$EnginePluginPath = "$UE5Path\Engine\Plugins\Developer\$PluginName"

Write-Host "=== UE5 Blueprint Plugin Generator Installation Script ===" -ForegroundColor Cyan
Write-Host "Source Path: $SourcePath" -ForegroundColor Yellow
Write-Host "UE5.4 Path: $UE5Path" -ForegroundColor Yellow
Write-Host "Install Method: $InstallMethod" -ForegroundColor Yellow

# Function to check if running as administrator
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# Function to verify UE5.4 installation
function Test-UE5Installation {
    if (-not (Test-Path $UE5Path)) {
        Write-Error "UE5.4 installation not found at: $UE5Path"
        return $false
    }
    
    $requiredPaths = @(
        "$UE5Path\Engine",
        "$UE5Path\Engine\Plugins",
        "$UE5Path\Engine\Binaries",
        "$UE5Path\Engine\Source"
    )
    
    foreach ($path in $requiredPaths) {
        if (-not (Test-Path $path)) {
            Write-Error "Required UE5.4 directory not found: $path"
            return $false
        }
    }
    
    Write-Host "✅ UE5.4 installation verified" -ForegroundColor Green
    return $true
}

# Function to verify plugin source files
function Test-PluginSource {
    $requiredFiles = @(
        "UE5BlueprintGenerator.uplugin",
        "Source\UE5BlueprintGenerator\UE5BlueprintGenerator.Build.cs",
        "Source\UE5BlueprintGenerator\Public\UE5BlueprintGeneratorModule.h",
        "Source\UE5BlueprintGenerator\Private\UE5BlueprintGeneratorModule.cpp"
    )
    
    foreach ($file in $requiredFiles) {
        $fullPath = Join-Path $SourcePath $file
        if (-not (Test-Path $fullPath)) {
            Write-Error "Required plugin file not found: $fullPath"
            return $false
        }
    }
    
    Write-Host "✅ Plugin source files verified" -ForegroundColor Green
    return $true
}

# Function to copy plugin files
function Copy-PluginFiles {
    param(
        [string]$DestinationPath
    )
    
    Write-Host "📁 Copying plugin files to: $DestinationPath" -ForegroundColor Yellow
    
    # Create destination directory if it doesn't exist
    if (-not (Test-Path $DestinationPath)) {
        New-Item -ItemType Directory -Path $DestinationPath -Force | Out-Null
        Write-Host "Created directory: $DestinationPath" -ForegroundColor Green
    }
    
    # Copy plugin descriptor
    Copy-Item -Path "$SourcePath\UE5BlueprintGenerator.uplugin" -Destination $DestinationPath -Force
    Write-Host "✅ Copied plugin descriptor" -ForegroundColor Green
    
    # Copy Source directory
    $sourceDir = "$SourcePath\Source"
    $destSourceDir = "$DestinationPath\Source"
    
    if (Test-Path $sourceDir) {
        Copy-Item -Path $sourceDir -Destination $destSourceDir -Recurse -Force
        Write-Host "✅ Copied Source directory" -ForegroundColor Green
    }
    
    # Copy docs directory (optional)
    $docsDir = "$SourcePath\docs"
    $destDocsDir = "$DestinationPath\docs"
    
    if (Test-Path $docsDir) {
        Copy-Item -Path $docsDir -Destination $destDocsDir -Recurse -Force
        Write-Host "✅ Copied docs directory" -ForegroundColor Green
    }
    
    # Copy additional files
    $additionalFiles = @("CHANGELOG.md", "PROJECT_PLAN.md", "SETUP_GUIDE.md")
    foreach ($file in $additionalFiles) {
        $sourceFile = "$SourcePath\$file"
        if (Test-Path $sourceFile) {
            Copy-Item -Path $sourceFile -Destination $DestinationPath -Force
            Write-Host "✅ Copied $file" -ForegroundColor Green
        }
    }
}

# Function to install as engine plugin
function Install-EnginePlugin {
    Write-Host "🔧 Installing as Engine Plugin..." -ForegroundColor Cyan
    
    # Check if plugin already exists
    if ((Test-Path $EnginePluginPath) -and -not $Force) {
        Write-Warning "Plugin already exists at: $EnginePluginPath"
        $response = Read-Host "Do you want to overwrite? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "Installation cancelled." -ForegroundColor Yellow
            return $false
        }
    }
    
    # Copy plugin files
    Copy-PluginFiles -DestinationPath $EnginePluginPath
    
    Write-Host "✅ Engine plugin installation completed!" -ForegroundColor Green
    Write-Host "Plugin installed at: $EnginePluginPath" -ForegroundColor Yellow
    
    return $true
}

# Function to install as project plugin
function Install-ProjectPlugin {
    if (-not $ProjectPath) {
        Write-Error "Project path is required for project plugin installation"
        return $false
    }
    
    if (-not (Test-Path $ProjectPath)) {
        Write-Error "Project path not found: $ProjectPath"
        return $false
    }
    
    Write-Host "🔧 Installing as Project Plugin..." -ForegroundColor Cyan
    
    $projectPluginPath = "$ProjectPath\Plugins\$PluginName"
    
    # Check if plugin already exists
    if ((Test-Path $projectPluginPath) -and -not $Force) {
        Write-Warning "Plugin already exists at: $projectPluginPath"
        $response = Read-Host "Do you want to overwrite? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Host "Installation cancelled." -ForegroundColor Yellow
            return $false
        }
    }
    
    # Copy plugin files
    Copy-PluginFiles -DestinationPath $projectPluginPath
    
    Write-Host "✅ Project plugin installation completed!" -ForegroundColor Green
    Write-Host "Plugin installed at: $projectPluginPath" -ForegroundColor Yellow
    
    return $true
}

# Function to regenerate project files
function Invoke-ProjectFileGeneration {
    Write-Host "🔄 Regenerating project files..." -ForegroundColor Cyan
    
    $generateScript = "$UE5Path\GenerateProjectFiles.bat"
    
    if (-not (Test-Path $generateScript)) {
        Write-Warning "GenerateProjectFiles.bat not found at: $generateScript"
        Write-Host "You may need to manually regenerate project files." -ForegroundColor Yellow
        return $false
    }
    
    try {
        Push-Location $UE5Path
        & $generateScript
        Write-Host "✅ Project files regenerated successfully!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "Failed to regenerate project files: $_"
        return $false
    }
    finally {
        Pop-Location
    }
}

# Function to display next steps
function Show-NextSteps {
    Write-Host "`n=== Next Steps ===" -ForegroundColor Cyan
    
    if ($InstallMethod -eq "Engine") {
        Write-Host "1. 🔄 Regenerate UE5 project files (optional - script attempted this)" -ForegroundColor Yellow
        Write-Host "   - Navigate to: $UE5Path" -ForegroundColor Gray
        Write-Host "   - Run: GenerateProjectFiles.bat" -ForegroundColor Gray
        Write-Host ""
        Write-Host "2. 🔨 Build UE5 with the plugin:" -ForegroundColor Yellow
        Write-Host "   - Open UE5.sln in Visual Studio" -ForegroundColor Gray
        Write-Host "   - Set configuration to 'Development Editor'" -ForegroundColor Gray
        Write-Host "   - Build solution (Ctrl+Shift+B)" -ForegroundColor Gray
        Write-Host ""
        Write-Host "3. 🎮 Test the plugin:" -ForegroundColor Yellow
        Write-Host "   - Launch UE5.4 Editor" -ForegroundColor Gray
        Write-Host "   - Create/open a project" -ForegroundColor Gray
        Write-Host "   - Go to Edit → Plugins" -ForegroundColor Gray
        Write-Host "   - Search for 'UE5 Blueprint Generator'" -ForegroundColor Gray
        Write-Host "   - Enable the plugin and restart editor" -ForegroundColor Gray
        Write-Host "   - Check Tools menu for 'Blueprint Generator'" -ForegroundColor Gray
    } else {
        Write-Host "1. 🔄 Regenerate project files:" -ForegroundColor Yellow
        Write-Host "   - Close UE5 Editor" -ForegroundColor Gray
        Write-Host "   - Right-click on .uproject file" -ForegroundColor Gray
        Write-Host "   - Select 'Generate Visual Studio project files'" -ForegroundColor Gray
        Write-Host ""
        Write-Host "2. 🔨 Build the project:" -ForegroundColor Yellow
        Write-Host "   - Open project .sln in Visual Studio" -ForegroundColor Gray
        Write-Host "   - Build solution" -ForegroundColor Gray
        Write-Host ""
        Write-Host "3. 🎮 Test the plugin:" -ForegroundColor Yellow
        Write-Host "   - Launch project in UE5 Editor" -ForegroundColor Gray
        Write-Host "   - Plugin should be automatically enabled" -ForegroundColor Gray
        Write-Host "   - Check Tools menu for 'Blueprint Generator'" -ForegroundColor Gray
    }
    
    Write-Host ""
    Write-Host "📚 Additional Resources:" -ForegroundColor Cyan
    Write-Host "   - Setup Guide: SETUP_GUIDE.md" -ForegroundColor Gray
    Write-Host "   - Documentation: docs/ folder" -ForegroundColor Gray
    Write-Host "   - Changelog: CHANGELOG.md" -ForegroundColor Gray
}

# Main execution
try {
    # Check administrator privileges for engine installation
    if ($InstallMethod -eq "Engine" -and -not (Test-Administrator)) {
        Write-Error "Administrator privileges required for engine plugin installation."
        Write-Host "Please run PowerShell as Administrator and try again." -ForegroundColor Yellow
        exit 1
    }
    
    # Verify prerequisites
    if (-not (Test-UE5Installation)) {
        exit 1
    }
    
    if (-not (Test-PluginSource)) {
        exit 1
    }
    
    # Perform installation
    $success = $false
    
    if ($InstallMethod -eq "Engine") {
        $success = Install-EnginePlugin
        
        # Attempt to regenerate project files
        if ($success) {
            Invoke-ProjectFileGeneration | Out-Null
        }
    } elseif ($InstallMethod -eq "Project") {
        $success = Install-ProjectPlugin
    } else {
        Write-Error "Invalid install method: $InstallMethod. Use 'Engine' or 'Project'"
        exit 1
    }
    
    if ($success) {
        Show-NextSteps
        Write-Host "`n🎉 Installation completed successfully!" -ForegroundColor Green
    } else {
        Write-Host "`n❌ Installation failed!" -ForegroundColor Red
        exit 1
    }
}
catch {
    Write-Error "Installation failed with error: $_"
    exit 1
} 