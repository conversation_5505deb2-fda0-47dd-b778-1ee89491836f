#pragma once

#include "CoreMinimal.h"
#include "Engine/Blueprint.h"
#include "K2Node.h"
#include "EdGraph/EdGraphPin.h"
#include "Templates/SharedPointer.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "HAL/CriticalSection.h"
#include "Misc/DateTime.h"
#include "BlueprintGenerationTypes.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogBlueprintGeneration, Log, All);

// Forward declarations
class UBlueprint;
class UEdGraph;
class UK2Node;
class UEdGraphPin;
class FKismetCompilerContext;

/**
 * Blueprint element type enumeration
 */
UENUM(BlueprintType)
enum class EBlueprintElementType : uint8
{
    Unknown,
    Function,
    Event,
    Variable,
    Component,
    CustomEvent,
    Macro,
    Interface,
    Struct,
    Enum
};

/**
 * Assembly strategy enumeration
 */
UENUM(BlueprintType)
enum class EAssemblyStrategy : uint8
{
    Sequential,
    Parallel,
    Hierarchical,
    TypeGrouped,
    PriorityBased
};

/**
 * Assembly priority enumeration
 */
UENUM(BlueprintType)
enum class EAssemblyPriority : uint8
{
    Low,
    Normal,
    High,
    Critical
};

/**
 * Validation level enumeration
 */
UENUM(BlueprintType)
enum class EValidationLevel : uint8
{
    None,
    Basic,
    Strict,
    Advanced,
    Complete
};

/**
 * Compilation mode enumeration
 */
UENUM(BlueprintType)
enum class ECompilationMode : uint8
{
    Standard,
    Debug,
    Release,
    Development,
    Shipping
};

/**
 * Error severity enumeration
 */
UENUM(BlueprintType)
enum class EErrorSeverity : uint8
{
    Info,
    Warning,
    Error,
    Fatal
};

/**
 * Blueprint creation request structure
 */
USTRUCT(BlueprintType)
struct FGENERALBLUEPRINTCREATIONREQUEST
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FString BlueprintName;

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    EBlueprintElementType ElementType = EBlueprintElementType::Function;

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FString NaturalLanguageDescription;

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    FString FolderPath = TEXT("/Game/Blueprints");

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bGenerateComments = true;

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bIncludeErrorHandling = true;

    UPROPERTY(BlueprintReadWrite, Category = "Request")
    bool bOptimizePerformance = true;

    FGENERALBLUEPRINTCREATIONREQUEST()
    {
        BlueprintName = TEXT("");
        ElementType = EBlueprintElementType::Function;
        NaturalLanguageDescription = TEXT("");
        FolderPath = TEXT("/Game/Blueprints");
        bGenerateComments = true;
        bIncludeErrorHandling = true;
        bOptimizePerformance = true;
    }
};

/**
 * Blueprint creation result structure
 */
USTRUCT(BlueprintType)
struct FGENERALBLUEPRINTCREATIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString BlueprintPath;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float CreationTime = 0.0f;

    FGENERALBLUEPRINTCREATIONRESULT()
    {
        bSuccess = false;
        BlueprintPath = TEXT("");
        ErrorMessage = TEXT("");
        CreationTime = 0.0f;
    }
};

/**
 * Assembly configuration structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYCONFIG
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    EAssemblyStrategy Strategy = EAssemblyStrategy::Sequential;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    EValidationLevel ValidationLevel = EValidationLevel::Basic;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bEnableParallelProcessing = false;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bEnableOptimization = true;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    int32 MaxRetryAttempts = 3;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    float TimeoutSeconds = 30.0f;

    FASSEMBLYCONFIG()
    {
        Strategy = EAssemblyStrategy::Sequential;
        ValidationLevel = EValidationLevel::Basic;
        bEnableParallelProcessing = false;
        bEnableOptimization = true;
        MaxRetryAttempts = 3;
        TimeoutSeconds = 30.0f;
    }
};

/**
 * Assembly progress structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYPROGRESS
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FString TaskId;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FString TaskName;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    float ProgressPercentage = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    FString CurrentStep;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 CompletedSteps = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Progress")
    int32 TotalSteps = 0;

    FASSEMBLYPROGRESS()
    {
        TaskId = TEXT("");
        TaskName = TEXT("");
        ProgressPercentage = 0.0f;
        CurrentStep = TEXT("");
        CompletedSteps = 0;
        TotalSteps = 0;
    }
};

/**
 * Assembly task structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYTASK
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FString TaskId;

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FString TaskName;

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    EAssemblyPriority Priority = EAssemblyPriority::Normal;

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FDateTime StartTime;

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    FDateTime EndTime;

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    bool bIsCompleted = false;

    UPROPERTY(BlueprintReadOnly, Category = "Task")
    bool bHasErrors = false;

    FASSEMBLYTASK()
    {
        TaskId = TEXT("");
        TaskName = TEXT("");
        Priority = EAssemblyPriority::Normal;
        StartTime = FDateTime::Now();
        EndTime = FDateTime::Now();
        bIsCompleted = false;
        bHasErrors = false;
    }
};

/**
 * Assembly result structure
 */
USTRUCT(BlueprintType)
struct FASSEMBLYRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString TaskId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Warnings;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float ExecutionTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    int32 NodesCreated = 0;

    FASSEMBLYRESULT()
    {
        bSuccess = false;
        TaskId = TEXT("");
        ErrorMessage = TEXT("");
        Warnings.Empty();
        ExecutionTime = 0.0f;
        NodesCreated = 0;
    }
};

/**
 * Complexity estimation result structure
 */
USTRUCT(BlueprintType)
struct FCOMPLEXITYESTIMATIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Complexity")
    float ComplexityScore = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Complexity")
    FString ComplexityLevel;

    UPROPERTY(BlueprintReadOnly, Category = "Complexity")
    TArray<FString> ComplexityFactors;

    UPROPERTY(BlueprintReadOnly, Category = "Complexity")
    bool bIsWithinCapabilities = true;

    UPROPERTY(BlueprintReadOnly, Category = "Complexity")
    FString RecommendedApproach;

    FCOMPLEXITYESTIMATIONRESULT()
    {
        ComplexityScore = 0.0f;
        ComplexityLevel = TEXT("Simple");
        ComplexityFactors.Empty();
        bIsWithinCapabilities = true;
        RecommendedApproach = TEXT("");
    }
};

/**
 * Graph validation result structure
 */
USTRUCT(BlueprintType)
struct FGRAPHVALIDATIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bIsValid = true;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> Errors;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FString> Warnings;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int32 NodesValidated = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int32 ConnectionsValidated = 0;

    FGRAPHVALIDATIONRESULT()
    {
        bIsValid = true;
        Errors.Empty();
        Warnings.Empty();
        NodesValidated = 0;
        ConnectionsValidated = 0;
    }
};

/**
 * Assembly validation issue structure
 */
USTRUCT(BlueprintType)
struct FGENERALASSEMBLYVALIDATIONISSUE
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    EErrorSeverity Severity = EErrorSeverity::Warning;

    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString IssueType;

    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString Description;

    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString NodeId;

    UPROPERTY(BlueprintReadOnly, Category = "Issue")
    FString SuggestedFix;

    FGENERALASSEMBLYVALIDATIONISSUE()
    {
        Severity = EErrorSeverity::Warning;
        IssueType = TEXT("");
        Description = TEXT("");
        NodeId = TEXT("");
        SuggestedFix = TEXT("");
    }
};

/**
 * Assembly validation result structure
 */
USTRUCT(BlueprintType)
struct FGENERALASSEMBLYVALIDATIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    bool bIsValid = true;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    TArray<FGENERALASSEMBLYVALIDATIONISSUE> Issues;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int32 ErrorCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    int32 WarningCount = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Validation")
    float ValidationTime = 0.0f;

    FGENERALASSEMBLYVALIDATIONRESULT()
    {
        bIsValid = true;
        Issues.Empty();
        ErrorCount = 0;
        WarningCount = 0;
        ValidationTime = 0.0f;
    }
};

/**
 * Compilation error structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONERROR
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    EErrorSeverity Severity = EErrorSeverity::Error;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorType;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString NodeName;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString SourceLocation;

    UPROPERTY(BlueprintReadOnly, Category = "Error")
    FString SuggestedFix;

    FCOMPILATIONERROR()
    {
        Severity = EErrorSeverity::Error;
        ErrorType = TEXT("");
        ErrorMessage = TEXT("");
        NodeName = TEXT("");
        SourceLocation = TEXT("");
        SuggestedFix = TEXT("");
    }
};

/**
 * Error context structure
 */
USTRUCT(BlueprintType)
struct FERRORCONTEXT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString ContextType;

    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString BlueprintName;

    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString GraphName;

    UPROPERTY(BlueprintReadOnly, Category = "Context")
    FString NodeName;

    UPROPERTY(BlueprintReadOnly, Category = "Context")
    TMap<FString, FString> AdditionalData;

    FERRORCONTEXT()
    {
        ContextType = TEXT("");
        BlueprintName = TEXT("");
        GraphName = TEXT("");
        NodeName = TEXT("");
        AdditionalData.Empty();
    }
};

/**
 * Error analysis result structure
 */
USTRUCT(BlueprintType)
struct FERRORANALYSISRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    FString ErrorCategory;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    FString RootCause;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> PossibleSolutions;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float ConfidenceLevel = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    bool bCanAutoRecover = false;

    FERRORANALYSISRESULT()
    {
        ErrorCategory = TEXT("");
        RootCause = TEXT("");
        PossibleSolutions.Empty();
        ConfidenceLevel = 0.0f;
        bCanAutoRecover = false;
    }
};

/**
 * Error recovery result structure
 */
USTRUCT(BlueprintType)
struct FERRORRECOVERYRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    bool bRecoverySuccessful = false;

    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    FString RecoveryMethod;

    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    FString RecoveryDetails;

    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    TArray<FString> ActionsPerformed;

    UPROPERTY(BlueprintReadOnly, Category = "Recovery")
    float RecoveryTime = 0.0f;

    FERRORRECOVERYRESULT()
    {
        bRecoverySuccessful = false;
        RecoveryMethod = TEXT("");
        RecoveryDetails = TEXT("");
        ActionsPerformed.Empty();
        RecoveryTime = 0.0f;
    }
};

/**
 * Compilation configuration structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONCONFIG
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    ECompilationMode Mode = ECompilationMode::Standard;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bEnableOptimization = true;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bGenerateDebugInfo = false;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bTreatWarningsAsErrors = false;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    float TimeoutSeconds = 60.0f;

    FCOMPILATIONCONFIG()
    {
        Mode = ECompilationMode::Standard;
        bEnableOptimization = true;
        bGenerateDebugInfo = false;
        bTreatWarningsAsErrors = false;
        TimeoutSeconds = 60.0f;
    }
};

/**
 * Compilation result structure
 */
USTRUCT(BlueprintType)
struct FCOMPILATIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FCOMPILATIONERROR> Errors;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FCOMPILATIONERROR> Warnings;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float CompilationTime = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString BlueprintPath;

    FCOMPILATIONRESULT()
    {
        bSuccess = false;
        Errors.Empty();
        Warnings.Empty();
        CompilationTime = 0.0f;
        BlueprintPath = TEXT("");
    }
};

/**
 * Node assembly instruction structure
 */
USTRUCT(BlueprintType)
struct FGENERALNODEASSEMBLYINSTRUCTION
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString InstructionId;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeType;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeName;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    EAssemblyPriority Priority = EAssemblyPriority::Normal;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FVector2D Position = FVector2D::ZeroVector;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TMap<FString, FString> Properties;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    TArray<FString> Dependencies;

    FGENERALNODEASSEMBLYINSTRUCTION()
    {
        InstructionId = TEXT("");
        NodeType = TEXT("");
        NodeName = TEXT("");
        Priority = EAssemblyPriority::Normal;
        Position = FVector2D::ZeroVector;
        Properties.Empty();
        Dependencies.Empty();
    }
};

/**
 * Node assembly configuration structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYCONFIG
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    EAssemblyStrategy Strategy = EAssemblyStrategy::Sequential;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bEnablePositionOptimization = true;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    bool bEnableConnectionValidation = true;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    float NodeSpacing = 400.0f;

    UPROPERTY(BlueprintReadWrite, Category = "Config")
    int32 MaxRetryAttempts = 3;

    FNODEASSEMBLYCONFIG()
    {
        Strategy = EAssemblyStrategy::Sequential;
        bEnablePositionOptimization = true;
        bEnableConnectionValidation = true;
        NodeSpacing = 400.0f;
        MaxRetryAttempts = 3;
    }
};

/**
 * Node assembly result structure
 */
USTRUCT(BlueprintType)
struct FNODEASSEMBLYRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString InstructionId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString NodeId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float AssemblyTime = 0.0f;

    FNODEASSEMBLYRESULT()
    {
        bSuccess = false;
        InstructionId = TEXT("");
        NodeId = TEXT("");
        ErrorMessage = TEXT("");
        AssemblyTime = 0.0f;
    }
};

/**
 * Assembly execution result structure
 */
USTRUCT(BlueprintType)
struct FGENERALASSEMBLYEXECUTIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FNODEASSEMBLYRESULT> NodeResults;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Errors;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> Warnings;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float TotalExecutionTime = 0.0f;

    FGENERALASSEMBLYEXECUTIONRESULT()
    {
        bSuccess = false;
        NodeResults.Empty();
        Errors.Empty();
        Warnings.Empty();
        TotalExecutionTime = 0.0f;
    }
};

/**
 * Connection assembly result structure
 */
USTRUCT(BlueprintType)
struct FCONNECTIONASSEMBLYRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ConnectionId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString SourceNodeId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString TargetNodeId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    FCONNECTIONASSEMBLYRESULT()
    {
        bSuccess = false;
        ConnectionId = TEXT("");
        SourceNodeId = TEXT("");
        TargetNodeId = TEXT("");
        ErrorMessage = TEXT("");
    }
};

/**
 * Property assembly instruction structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYINSTRUCTION
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString InstructionId;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString NodeId;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString PropertyName;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString PropertyValue;

    UPROPERTY(BlueprintReadOnly, Category = "Instruction")
    FString PropertyType;

    FPROPERTYASSEMBLYINSTRUCTION()
    {
        InstructionId = TEXT("");
        NodeId = TEXT("");
        PropertyName = TEXT("");
        PropertyValue = TEXT("");
        PropertyType = TEXT("");
    }
};

/**
 * Property assembly result structure
 */
USTRUCT(BlueprintType)
struct FPROPERTYASSEMBLYRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString InstructionId;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString PropertyName;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float AssemblyTime = 0.0f;

    FPROPERTYASSEMBLYRESULT()
    {
        bSuccess = false;
        InstructionId = TEXT("");
        PropertyName = TEXT("");
        ErrorMessage = TEXT("");
        AssemblyTime = 0.0f;
    }
};

/**
 * Optimization rule structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONRULE
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Rule")
    FString RuleName;

    UPROPERTY(BlueprintReadWrite, Category = "Rule")
    FString Description;

    UPROPERTY(BlueprintReadWrite, Category = "Rule")
    bool bIsEnabled = true;

    UPROPERTY(BlueprintReadWrite, Category = "Rule")
    EAssemblyPriority Priority = EAssemblyPriority::Normal;

    UPROPERTY(BlueprintReadWrite, Category = "Rule")
    TArray<FString> ApplicableNodeTypes;

    FOPTIMIZATIONRULE()
    {
        RuleName = TEXT("");
        Description = TEXT("");
        bIsEnabled = true;
        Priority = EAssemblyPriority::Normal;
        ApplicableNodeTypes.Empty();
    }
};

/**
 * Optimization analysis result structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONANALYSISRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float PerformanceScore = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float MemoryScore = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float ReadabilityScore = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    TArray<FString> ImprovementSuggestions;

    UPROPERTY(BlueprintReadOnly, Category = "Analysis")
    float PotentialImprovement = 0.0f;

    FOPTIMIZATIONANALYSISRESULT()
    {
        PerformanceScore = 0.0f;
        MemoryScore = 0.0f;
        ReadabilityScore = 0.0f;
        ImprovementSuggestions.Empty();
        PotentialImprovement = 0.0f;
    }
};

/**
 * Optimization result structure
 */
USTRUCT(BlueprintType)
struct FOPTIMIZATIONRESULT
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    bool bSuccess = false;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    TArray<FString> AppliedOptimizations;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float PerformanceImprovement = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float MemoryImprovement = 0.0f;

    UPROPERTY(BlueprintReadOnly, Category = "Result")
    float OptimizationTime = 0.0f;

    FOPTIMIZATIONRESULT()
    {
        bSuccess = false;
        AppliedOptimizations.Empty();
        PerformanceImprovement = 0.0f;
        MemoryImprovement = 0.0f;
        OptimizationTime = 0.0f;
    }
}; 