#pragma once

#include "CoreMinimal.h"
#include "Templates/SharedPointer.h"
#include "Engine/Blueprint.h"
#include "BlueprintGraph/Classes/K2Node.h"
#include "CodeGenerationFramework.h"
#include "BlueprintToCppConverter.generated.h"

DECLARE_LOG_CATEGORY_EXTERN(LogBlueprintToCppConverter, Log, All);

// Forward declarations
class UBlueprint;
class UBlueprintGeneratedClass;
class UK2Node;
class UEdGraphNode;
class UEdGraphPin;

/**
 * Enumeration for C++ conversion modes
 */
UENUM(BlueprintType)
enum class ECppConversionMode : uint8
{
    HeaderOnly,          // Generate only header file
    ImplementationOnly,  // Generate only implementation file
    Complete,           // Generate both header and implementation
    Interface,          // Generate interface class
    Component,          // Generate component class
    Actor,              // Generate actor class
    Custom              // Custom conversion mode
};

/**
 * Enumeration for C++ code style preferences
 */
UENUM(BlueprintType)
enum class ECppCodeStyle : uint8
{
    UE5Standard,        // Standard UE5 coding conventions
    Compact,            // Compact code style
    Verbose,            // Verbose with extensive comments
    Modern,             // Modern C++ features
    Legacy,             // Legacy C++ compatibility
    Custom              // Custom code style
};

/**
 * Enumeration for variable access patterns
 */
UENUM(BlueprintType)
enum class ECppAccessPattern : uint8
{
    Public,             // Public access
    Protected,          // Protected access
    Private,            // Private access
    BlueprintReadOnly,  // Blueprint read-only
    BlueprintReadWrite, // Blueprint read-write
    EditAnywhere,       // Edit anywhere
    EditDefaultsOnly,   // Edit defaults only
    VisibleAnywhere,    // Visible anywhere
    VisibleDefaultsOnly // Visible defaults only
};

/**
 * Structure representing a C++ variable declaration
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppVariable
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    FString Name;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    FString Type;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    FString DefaultValue;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    ECppAccessPattern AccessPattern;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    TArray<FString> Specifiers;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    FString Comment;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    bool bIsArray;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    bool bIsPointer;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    bool bIsReference;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    bool bIsConst;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Variable")
    bool bIsStatic;

    FCppVariable()
        : AccessPattern(ECppAccessPattern::Private)
        , bIsArray(false)
        , bIsPointer(false)
        , bIsReference(false)
        , bIsConst(false)
        , bIsStatic(false)
    {}
};

/**
 * Structure representing a C++ function declaration
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppFunction
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    FString Name;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    FString ReturnType;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    TArray<FCppVariable> Parameters;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    ECppAccessPattern AccessPattern;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    TArray<FString> Specifiers;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    FString Comment;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    FString Implementation;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    bool bIsVirtual;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    bool bIsOverride;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    bool bIsConst;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    bool bIsStatic;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    bool bIsPure;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Function")
    bool bIsInline;

    FCppFunction()
        : ReturnType(TEXT("void"))
        , AccessPattern(ECppAccessPattern::Public)
        , bIsVirtual(false)
        , bIsOverride(false)
        , bIsConst(false)
        , bIsStatic(false)
        , bIsPure(false)
        , bIsInline(false)
    {}
};

/**
 * Structure representing a C++ class declaration
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppClass
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    FString Name;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    FString BaseClass;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    TArray<FString> Interfaces;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    TArray<FCppVariable> Variables;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    TArray<FCppFunction> Functions;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    TArray<FString> Includes;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    TArray<FString> ForwardDeclarations;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    FString Namespace;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    FString Comment;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    TArray<FString> Specifiers;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    bool bIsAbstract;

    UPROPERTY(BlueprintReadWrite, Category = "C++ Class")
    bool bIsFinal;

    FCppClass()
        : bIsAbstract(false)
        , bIsFinal(false)
    {}
};

/**
 * Structure for C++ conversion configuration
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppConversionConfig
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    ECppConversionMode ConversionMode;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    ECppCodeStyle CodeStyle;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    FString OutputDirectory;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    FString HeaderFileExtension;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    FString ImplementationFileExtension;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    bool bGenerateComments;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    bool bGenerateDocumentation;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    bool bUseModernCpp;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    bool bOptimizeForPerformance;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    bool bIncludeDebugInfo;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    int32 IndentSize;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    bool bUseTabs;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Config")
    int32 MaxLineLength;

    FCppConversionConfig()
        : ConversionMode(ECppConversionMode::Complete)
        , CodeStyle(ECppCodeStyle::UE5Standard)
        , HeaderFileExtension(TEXT(".h"))
        , ImplementationFileExtension(TEXT(".cpp"))
        , bGenerateComments(true)
        , bGenerateDocumentation(true)
        , bUseModernCpp(true)
        , bOptimizeForPerformance(false)
        , bIncludeDebugInfo(false)
        , IndentSize(4)
        , bUseTabs(true)
        , MaxLineLength(120)
    {}
};

/**
 * Structure for C++ conversion request
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppConversionRequest
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Request")
    TWeakObjectPtr<UBlueprint> Blueprint;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Request")
    FCppConversionConfig Config;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Request")
    FString RequestId;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Request")
    int32 Priority;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Request")
    float Timestamp;

    FCppConversionRequest()
        : Priority(0)
        , Timestamp(0.0f)
    {}
};

/**
 * Structure for C++ conversion result
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppConversionResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    FString RequestId;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    bool bSuccess;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    FString ErrorMessage;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    FCppClass GeneratedClass;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    FString HeaderCode;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    FString ImplementationCode;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    TArray<FString> GeneratedFiles;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    float ConversionTime;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    int32 LinesOfCode;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Result")
    float QualityScore;

    FCppConversionResult()
        : bSuccess(false)
        , ConversionTime(0.0f)
        , LinesOfCode(0)
        , QualityScore(0.0f)
    {}
};

/**
 * Structure for C++ conversion statistics
 */
USTRUCT(BlueprintType)
struct BLUEPRINTGENERATOR_API FCppConversionStatistics
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    int32 TotalConversions;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    int32 SuccessfulConversions;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    int32 FailedConversions;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    float AverageConversionTime;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    float AverageQualityScore;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    int32 TotalLinesGenerated;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    TMap<FString, int32> ConversionModeUsage;

    UPROPERTY(BlueprintReadWrite, Category = "Conversion Statistics")
    TMap<FString, int32> CodeStyleUsage;

    FCppConversionStatistics()
        : TotalConversions(0)
        , SuccessfulConversions(0)
        , FailedConversions(0)
        , AverageConversionTime(0.0f)
        , AverageQualityScore(0.0f)
        , TotalLinesGenerated(0)
    {}
};

// Event delegates for C++ conversion monitoring
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnCppConversionStarted, const FString&, RequestId);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCppConversionProgress, const FString&, RequestId, float, Progress);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCppConversionCompleted, const FString&, RequestId, const FCppConversionResult&, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnCppConversionError, const FString&, RequestId, const FString&, ErrorMessage);

/**
 * Interface for C++ code generators
 */
class BLUEPRINTGENERATOR_API ICppCodeGenerator
{
public:
    virtual ~ICppCodeGenerator() = default;

    /**
     * Generate C++ code from blueprint class
     */
    virtual FCppConversionResult GenerateCode(const FCppConversionRequest& Request) = 0;

    /**
     * Get generator name
     */
    virtual FString GetGeneratorName() const = 0;

    /**
     * Get supported conversion modes
     */
    virtual TArray<ECppConversionMode> GetSupportedModes() const = 0;

    /**
     * Get generator priority (higher = preferred)
     */
    virtual int32 GetPriority() const = 0;

    /**
     * Check if generator can handle the request
     */
    virtual bool CanHandleRequest(const FCppConversionRequest& Request) const = 0;
};

/**
 * Main Blueprint to C++ Converter class
 * Handles conversion of UE5 blueprints to C++ code
 */
UCLASS(BlueprintType, Blueprintable)
class BLUEPRINTGENERATOR_API UBlueprintToCppConverter : public UObject
{
    GENERATED_BODY()

public:
    UBlueprintToCppConverter();

    /**
     * Get singleton instance
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    static UBlueprintToCppConverter* GetInstance();

    /**
     * Convert blueprint to C++ code
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    FCppConversionResult ConvertBlueprint(const FCppConversionRequest& Request);

    /**
     * Convert blueprint to C++ code asynchronously
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    FString ConvertBlueprintAsync(const FCppConversionRequest& Request);

    /**
     * Register a C++ code generator
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    bool RegisterGenerator(TSharedPtr<ICppCodeGenerator> Generator);

    /**
     * Unregister a C++ code generator
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    bool UnregisterGenerator(const FString& GeneratorName);

    /**
     * Get available generators
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    TArray<FString> GetAvailableGenerators() const;

    /**
     * Validate conversion request
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    bool ValidateRequest(const FCppConversionRequest& Request, FString& ErrorMessage) const;

    /**
     * Get conversion statistics
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    FCppConversionStatistics GetStatistics() const;

    /**
     * Reset statistics
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    void ResetStatistics();

    /**
     * Cancel conversion request
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    bool CancelConversion(const FString& RequestId);

    /**
     * Get active conversion requests
     */
    UFUNCTION(BlueprintCallable, Category = "Blueprint to C++ Converter")
    TArray<FString> GetActiveRequests() const;

    // Event delegates
    UPROPERTY(BlueprintAssignable, Category = "Blueprint to C++ Converter")
    FOnCppConversionStarted OnConversionStarted;

    UPROPERTY(BlueprintAssignable, Category = "Blueprint to C++ Converter")
    FOnCppConversionProgress OnConversionProgress;

    UPROPERTY(BlueprintAssignable, Category = "Blueprint to C++ Converter")
    FOnCppConversionCompleted OnConversionCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Blueprint to C++ Converter")
    FOnCppConversionError OnConversionError;

protected:
    /**
     * Analyze blueprint structure
     */
    FCppClass AnalyzeBlueprintStructure(UBlueprint* Blueprint) const;

    /**
     * Extract variables from blueprint
     */
    TArray<FCppVariable> ExtractVariables(UBlueprint* Blueprint) const;

    /**
     * Extract functions from blueprint
     */
    TArray<FCppFunction> ExtractFunctions(UBlueprint* Blueprint) const;

    /**
     * Convert blueprint node to C++ code
     */
    FString ConvertNodeToCpp(UK2Node* Node, const FCppConversionConfig& Config) const;

    /**
     * Generate header file content
     */
    FString GenerateHeaderFile(const FCppClass& CppClass, const FCppConversionConfig& Config) const;

    /**
     * Generate implementation file content
     */
    FString GenerateImplementationFile(const FCppClass& CppClass, const FCppConversionConfig& Config) const;

    /**
     * Format C++ code according to style
     */
    FString FormatCode(const FString& Code, const FCppConversionConfig& Config) const;

    /**
     * Calculate quality score
     */
    float CalculateQualityScore(const FCppClass& CppClass, const FCppConversionConfig& Config) const;

    /**
     * Update statistics
     */
    void UpdateStatistics(const FCppConversionResult& Result);

    /**
     * Find best generator for request
     */
    TSharedPtr<ICppCodeGenerator> FindBestGenerator(const FCppConversionRequest& Request) const;

private:
    // Singleton instance
    static UBlueprintToCppConverter* Instance;

    // Registered generators
    TArray<TSharedPtr<ICppCodeGenerator>> RegisteredGenerators;

    // Active conversion requests
    TMap<FString, FCppConversionRequest> ActiveRequests;

    // Conversion statistics
    FCppConversionStatistics Statistics;

    // Thread safety
    mutable FCriticalSection CriticalSection;
}; 